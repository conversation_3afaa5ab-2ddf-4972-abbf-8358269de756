"""
基于条件表达式的JSON文件和图像文件批量删除功能

该模块实现了一个强大的批量删除工具，能够根据用户输入的条件表达式，
分析JSON标注文件中的标签数量，当条件满足时自动删除对应的JSON文件和图像文件。

主要功能：
- JSON文件扫描和标签统计
- 条件表达式解析和评估
- 安全的文件删除操作
- 详细的日志记录和UI反馈
- 配置化的UI控件管理

作者: AI Assistant
创建时间: 2025-01-31
"""

import json
import operator
import os
import re
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional, Union, Tuple, Any

from global_tools.postgre_sql import PostgreSQLClient
from global_tools.ui_tools import LogOutput, TextEditManager
from global_tools.utils import Logger


class TokenType( Enum ):
	"""词法分析器的标记类型"""
	LABEL = "LABEL"  # 标签名
	OPERATOR = "OPERATOR"  # 比较运算符 (>=, <=, ==, !=, >, <)
	NUMBER = "NUMBER"  # 数值
	AND = "AND"  # 逻辑与
	OR = "OR"  # 逻辑或
	NOT = "NOT"  # 逻辑非
	LPAREN = "LPAREN"  # 左括号 (
	RPAREN = "RPAREN"  # 右括号 )
	EOF = "EOF"  # 结束标记


@dataclass
class Token:
	"""词法分析器的标记"""
	type: TokenType
	value: str
	position: int


class ASTNode:
	"""抽象语法树节点基类"""
	pass


@dataclass
class ComparisonNode( ASTNode ):
	"""比较节点：标签名 运算符 数值"""
	label_name: str
	operator: str
	value: Union[ int, float ]


@dataclass
class LogicalNode( ASTNode ):
	"""逻辑运算节点：AND、OR、NOT"""
	operator: str
	left: Optional[ ASTNode ] = None
	right: Optional[ ASTNode ] = None


class ExpressionTokenizer:
	"""条件表达式词法分析器"""

	def __init__( self, expression: str ):
		self.expression = expression.strip()
		self.position = 0
		self.tokens = [ ]
		self.__tokenize()

	def __tokenize( self ) -> None:
		"""将表达式分解为标记序列"""
		while self.position < len( self.expression ):
			self.__skip_whitespace()

			if self.position >= len( self.expression ):
				break

			# 检查逻辑运算符
			if self.__match_keyword( "AND" ):
				self.tokens.append( Token( TokenType.AND, "AND", self.position ) )
				self.position += 3
			elif self.__match_keyword( "OR" ):
				self.tokens.append( Token( TokenType.OR, "OR", self.position ) )
				self.position += 2
			elif self.__match_keyword( "NOT" ):
				self.tokens.append( Token( TokenType.NOT, "NOT", self.position ) )
				self.position += 3
			# 检查括号
			elif self.expression[ self.position ] == '(':
				self.tokens.append( Token( TokenType.LPAREN, "(", self.position ) )
				self.position += 1
			elif self.expression[ self.position ] == ')':
				self.tokens.append( Token( TokenType.RPAREN, ")", self.position ) )
				self.position += 1
			# 检查比较运算符
			elif self.__match_operator():
				pass  # __match_operator 已经处理了标记添加
			# 检查数值
			elif self.__match_number():
				pass  # __match_number 已经处理了标记添加
			# 检查标签名
			elif self.__match_label():
				pass  # __match_label 已经处理了标记添加
			else:
				raise ValueError( f"无法识别的字符 '{self.expression[ self.position ]}' 在位置 {self.position}" )

		self.tokens.append( Token( TokenType.EOF, "", self.position ) )

	def __skip_whitespace( self ) -> None:
		"""跳过空白字符"""
		while self.position < len( self.expression ) and self.expression[ self.position ].isspace():
			self.position += 1

	def __match_keyword( self, keyword: str ) -> bool:
		"""匹配关键字"""
		if self.position + len( keyword ) <= len( self.expression ):
			if self.expression[ self.position:self.position + len( keyword ) ].upper() == keyword:
				# 确保关键字后面是空白字符或特殊字符
				next_pos = self.position + len( keyword )
				if next_pos >= len( self.expression ) or not self.expression[ next_pos ].isalnum():
					return True
		return False

	def __match_operator( self ) -> bool:
		"""匹配比较运算符"""
		operators = [ ">=", "<=", "==", "!=", ">", "<" ]
		for op in operators:
			if self.position + len( op ) <= len( self.expression ):
				if self.expression[ self.position:self.position + len( op ) ] == op:
					self.tokens.append( Token( TokenType.OPERATOR, op, self.position ) )
					self.position += len( op )
					return True
		return False

	def __match_number( self ) -> bool:
		"""匹配数值"""
		start_pos = self.position

		# 匹配整数或浮点数
		while self.position < len( self.expression ) and (
				self.expression[ self.position ].isdigit() or self.expression[ self.position ] == '.'
		):
			self.position += 1

		if self.position > start_pos:
			value = self.expression[ start_pos:self.position ]
			self.tokens.append( Token( TokenType.NUMBER, value, start_pos ) )
			return True
		return False

	def __match_label( self ) -> bool:
		"""匹配标签名"""
		start_pos = self.position

		# 标签名可以包含中文、英文、数字、下划线等
		while self.position < len( self.expression ):
			char = self.expression[ self.position ]
			if char.isalnum() or char == '_' or ord( char ) > 127:  # 支持中文字符
				self.position += 1
			else:
				break

		if self.position > start_pos:
			value = self.expression[ start_pos:self.position ]
			self.tokens.append( Token( TokenType.LABEL, value, start_pos ) )
			return True
		return False

	def get_tokens( self ) -> List[ Token ]:
		"""获取标记列表"""
		return self.tokens


class ConditionalFileBatchDeleter:
	"""
	基于条件表达式的JSON文件和图像文件批量删除器

	该类支持两种工作模式：
	1. 基于JSON文件的传统模式（execute_batch_deletion方法）
	2. 基于PostgreSQL数据库查询的新模式（execute_database_batch_deletion方法）

	该类能够根据用户输入的条件表达式，分析标签数量，
	当条件满足时自动删除对应的JSON文件和图像文件。

	主要特性：
	- 配置化的UI控件管理，避免硬编码控件名称
	- 支持复杂条件表达式（>=, <=, ==, !=, >, < 等运算符）
	- 支持PostgreSQL数据库查询模式，可查询segmentation_data或obb_data列
	- 安全的文件删除操作，包含完整的错误处理机制
	- 详细的日志记录，支持控制台和UI双重输出
	- 线程安全的UI操作
	- 数据库连接池管理和异常处理

	数据库模式新增功能：
	- 支持segment模式和obb模式的checkbox选择
	- 可配置数据库名称、表名和数据源筛选条件
	- 自动建立和管理PostgreSQL连接
	- 从数据库标注数据中提取标签信息
	- 支持复杂的数据库查询条件

	使用示例：
		# 创建管理器实例（包含新增的CheckBoxManager）
		line_edit_manager = LineEditManager(path_input_widget)
		text_edit_manager = TextEditManager(result_display_widget)
		checkbox_manager = CheckBoxManager(checkbox_container)
		logger = Logger()
		log_output = LogOutput(log_display_widget)

		# 创建批量删除器
		deleter = ConditionalFileBatchDeleter(
			line_edit_manager=line_edit_manager,
			text_edit_manager=text_edit_manager,
			checkbox_manager=checkbox_manager,
			logger=logger,
			log_output=log_output
		)

		# 传统JSON模式：执行批量删除操作
		result = deleter.execute_batch_deletion()

		# 数据库模式：执行基于数据库查询的批量删除操作
		result = deleter.execute_database_batch_deletion()
	"""

	def __init__(
			self, line_edit_manager=None, text_edit_manager: TextEditManager = None, checkbox_manager=None,
			logger: Logger = None, log_output: LogOutput = None
	):
		"""
		初始化批量删除器

		Args:
			line_edit_manager (LineEditManager, optional): 用于管理QLineEdit控件的实例
			text_edit_manager (TextEditManager, optional): 用于管理QTextEdit控件的实例
			checkbox_manager (CheckBoxManager, optional): 用于管理QCheckBox控件的实例
			logger (Logger, optional): 用于在控制台输出日志的实例
			log_output (LogOutput, optional): 用于在UI中输出突出重点日志的实例
		"""
		# 存储管理器实例
		self.__line_edit_manager = line_edit_manager
		self.__text_edit_manager = text_edit_manager
		self.__checkbox_manager = checkbox_manager
		self.__logger = logger
		self.__log_output = log_output

		# UI控件配置 - 修正条件表达式输入源配置
		self.__UI_WIDGET_CONFIG = {
			"input_controls": {
				"folder_path_input":          {
					"widget_id":           "lineEdit_48",
					"widget_type":         "QLineEdit",
					"description":         "文件夹路径输入框，用于指定要扫描的JSON文件目录",
					"default_value":       "",
					"validation_required": True,
					"manager_type":        "LineEditManager"
				},
				"condition_expression_input": {
					"widget_id":           "textEdit_6",
					"widget_type":         "QTextEdit",
					"description":         "条件表达式输入框，用于输入删除条件如'主任务 >= 1'或复杂表达式",
					"default_value":       "",
					"validation_required": True,
					"manager_type":        "TextEditManager"
				},
				"segment_mode_checkbox":      {
					"widget_id":           "rotate_146",
					"widget_type":         "QCheckBox",
					"description":         "segment模式选择器，选中时查询segmentation_data列",
					"default_value":       False,
					"validation_required": False,
					"manager_type":        "CheckBoxManager"
				},
				"obb_mode_checkbox":          {
					"widget_id":           "rotate_147",
					"widget_type":         "QCheckBox",
					"description":         "obb模式选择器，选中时查询obb_data列",
					"default_value":       False,
					"validation_required": False,
					"manager_type":        "CheckBoxManager"
				},
				"database_name_input":        {
					"widget_id":           "lineEdit_174",
					"widget_type":         "QLineEdit",
					"description":         "数据库名称输入框，用于指定要连接的PostgreSQL数据库名称",
					"default_value":       "",
					"validation_required": True,
					"manager_type":        "LineEditManager"
				},
				"table_name_input":           {
					"widget_id":           "lineEdit_175",
					"widget_type":         "QLineEdit",
					"description":         "数据库表名输入框，用于指定要查询的数据库表名",
					"default_value":       "",
					"validation_required": True,
					"manager_type":        "LineEditManager"
				},
				"data_source_filter_input":   {
					"widget_id":           "lineEdit_176",
					"widget_type":         "QLineEdit",
					"description":         "数据源筛选条件输入框，用于指定data_source字段的筛选条件",
					"default_value":       "",
					"validation_required": False,
					"manager_type":        "LineEditManager"
				}
			}
		}

		# 支持的运算符映射
		self.__OPERATORS = {
			'>=': operator.ge,
			'<=': operator.le,
			'==': operator.eq,
			'!=': operator.ne,
			'>':  operator.gt,
			'<':  operator.lt
		}

		# 统计信息
		self.__stats = {
			"scanned_files":       0,
			"matched_files":       0,
			"deleted_json_files":  0,
			"deleted_image_files": 0,
			"failed_deletions":    0,
			"errors":              [ ]
		}

		if self.__logger:
			self.__logger.info( "ConditionalFileBatchDeleter 初始化完成" )
		if self.__log_output:
			self.__log_output.append( "ConditionalFileBatchDeleter 初始化完成" )

	def __get_widget_config( self, config_key: str ) -> Dict[ str, Any ]:
		"""
		获取UI控件配置信息

		Args:
			config_key (str): 配置键名，如 'folder_path_input'

		Returns:
			Dict[str, Any]: 控件配置字典

		Raises:
			ValueError: 当配置键不存在时抛出异常
		"""
		# 在输入控件中查找
		if config_key in self.__UI_WIDGET_CONFIG[ "input_controls" ]:
			return self.__UI_WIDGET_CONFIG[ "input_controls" ][ config_key ]

		# 在输出控件中查找
		if config_key in self.__UI_WIDGET_CONFIG[ "output_controls" ]:
			return self.__UI_WIDGET_CONFIG[ "output_controls" ][ config_key ]

		raise ValueError( f"未找到控件配置: {config_key}" )

	def __get_input_value( self, config_key: str ) -> str:
		"""
		从UI控件获取输入值

		Args:
			config_key (str): 配置键名

		Returns:
			str: 控件中的文本值

		Raises:
			ValueError: 当无法获取控件值时抛出异常
		"""
		try:
			widget_config = self.__get_widget_config( config_key )
			widget_id = widget_config[ "widget_id" ]

			if widget_config[ "widget_type" ] == "QLineEdit":
				if self.__line_edit_manager and self.__line_edit_manager.has_line_edit( widget_id ):
					value = self.__line_edit_manager.get_text( widget_id )
					return value if value is not None else ""
				else:
					raise ValueError( f"LineEditManager 未管理控件: {widget_id}" )

			elif widget_config[ "widget_type" ] == "QTextEdit":
				if self.__text_edit_manager and self.__text_edit_manager.has_text_edit( widget_id ):
					value = self.__text_edit_manager.get_text( widget_id )
					return value if value is not None else ""
				else:
					raise ValueError( f"TextEditManager 未管理控件: {widget_id}" )

			else:
				raise ValueError( f"不支持的控件类型: {widget_config[ 'widget_type' ]}" )

		except Exception as e:
			if self.__logger:
				self.__logger.error( f"获取控件值失败 [{config_key}]: {str( e )}" )
			if self.__log_output:
				self.__log_output.append( f"获取控件值失败 [{config_key}]: {str( e )}", color="red" )
			raise

	def __set_output_value( self, config_key: str, value: str ) -> None:
		"""
		向UI控件设置输出值

		Args:
			config_key (str): 配置键名
			value (str): 要设置的文本值
		"""
		try:
			widget_config = self.__get_widget_config( config_key )
			widget_id = widget_config[ "widget_id" ]

			if widget_config[ "widget_type" ] == "QTextEdit":
				if self.__text_edit_manager and self.__text_edit_manager.has_text_edit( widget_id ):
					self.__text_edit_manager.set_text( widget_id, value )
				else:
					if self.__logger:
						self.__logger.warning( f"TextEditManager 未管理控件: {widget_id}" )
					if self.__log_output:
						self.__log_output.append( f"TextEditManager 未管理控件: {widget_id}", color="orange" )

			elif widget_config[ "widget_type" ] == "QLineEdit":
				if self.__line_edit_manager and self.__line_edit_manager.has_line_edit( widget_id ):
					self.__line_edit_manager.set_text( widget_id, value )
				else:
					if self.__logger:
						self.__logger.warning( f"LineEditManager 未管理控件: {widget_id}" )
					if self.__log_output:
						self.__log_output.append( f"LineEditManager 未管理控件: {widget_id}", color="orange" )

			else:
				if self.__logger:
					self.__logger.warning( f"不支持的输出控件类型: {widget_config[ 'widget_type' ]}" )
				if self.__log_output:
					self.__log_output.append(
						f"不支持的输出控件类型: {widget_config[ 'widget_type' ]}", color="orange"
					)

		except Exception as e:
			if self.__logger:
				self.__logger.error( f"设置控件值失败 [{config_key}]: {str( e )}" )
			if self.__log_output:
				self.__log_output.append( f"设置控件值失败 [{config_key}]: {str( e )}", color="red" )

	def __scan_json_files( self, folder_path: str ) -> List[ str ]:
		"""
		扫描指定文件夹中的所有JSON文件

		Args:
			folder_path (str): 要扫描的文件夹路径

		Returns:
			List[str]: JSON文件路径列表

		Raises:
			FileNotFoundError: 当文件夹不存在时抛出异常
			PermissionError: 当没有访问权限时抛出异常
		"""
		if not os.path.exists( folder_path ):
			raise FileNotFoundError( f"文件夹不存在: {folder_path}" )

		if not os.path.isdir( folder_path ):
			raise ValueError( f"路径不是文件夹: {folder_path}" )

		json_files = [ ]

		try:
			for file_name in os.listdir( folder_path ):
				if file_name.lower().endswith( '.json' ):
					file_path = os.path.join( folder_path, file_name )
					if os.path.isfile( file_path ):
						json_files.append( file_path )

			if self.__logger:
				self.__logger.info( f"扫描到 {len( json_files )} 个JSON文件", color="blue" )
			if self.__log_output:
				self.__log_output.append( f"扫描到 {len( json_files )} 个JSON文件", color="blue" )
			return json_files

		except PermissionError as e:
			if self.__logger:
				self.__logger.error( f"没有访问文件夹的权限: {folder_path}" )
			if self.__log_output:
				self.__log_output.append( f"没有访问文件夹的权限: {folder_path}", color="red" )
			raise
		except Exception as e:
			if self.__logger:
				self.__logger.error( f"扫描文件夹时发生错误: {str( e )}" )
			if self.__log_output:
				self.__log_output.append( f"扫描文件夹时发生错误: {str( e )}", color="red" )
			raise

	def __extract_labels_from_json( self, json_file_path: str ) -> Tuple[ List[ str ], str ]:
		"""
		从JSON文件中提取标签列表和图像路径

		Args:
			json_file_path (str): JSON文件路径

		Returns:
			Tuple[List[str], str]: (标签列表, 图像文件路径)

		Raises:
			json.JSONDecodeError: 当JSON格式错误时抛出异常
			KeyError: 当JSON结构不符合预期时抛出异常
		"""
		try:
			with open( json_file_path, 'r', encoding='utf-8' ) as f:
				data = json.load( f )

			# 提取标签列表
			labels = [ ]
			if 'shapes' in data and isinstance( data[ 'shapes' ], list ):
				for shape in data[ 'shapes' ]:
					if isinstance( shape, dict ) and 'label' in shape:
						labels.append( shape[ 'label' ] )

			# 提取图像路径
			image_path = ""
			if 'imagePath' in data:
				image_path = data[ 'imagePath' ]
				# 如果是相对路径，转换为绝对路径
				if not os.path.isabs( image_path ):
					json_dir = os.path.dirname( json_file_path )
					image_path = os.path.join( json_dir, image_path )

			return labels, image_path

		except json.JSONDecodeError as e:
			if self.__logger:
				self.__logger.error( f"JSON格式错误 [{json_file_path}]: {str( e )}" )
			if self.__log_output:
				self.__log_output.append( f"JSON格式错误 [{json_file_path}]: {str( e )}", color="red" )
			raise
		except Exception as e:
			if self.__logger:
				self.__logger.error( f"读取JSON文件失败 [{json_file_path}]: {str( e )}" )
			if self.__log_output:
				self.__log_output.append( f"读取JSON文件失败 [{json_file_path}]: {str( e )}", color="red" )
			raise

	def __extract_labels_from_database( self, image_filename: str, client: PostgreSQLClient ) -> Tuple[ List[ str ], str ]:
		"""
		从PostgreSQL数据库中提取标签列表和图像路径（优化版本）

		此方法是数据库模式的核心功能，负责：
		1. 获取数据库配置信息（数据库名、表名、筛选条件）
		2. 根据checkbox状态确定查询列（segmentation_data或obb_data）
		3. 使用外部传入的数据库连接执行查询（性能优化）
		4. 从查询结果中提取标签信息

		性能优化说明：
		- 使用外部传入的PostgreSQLClient实例，避免重复创建连接
		- 适用于批量处理场景，显著减少数据库连接开销
		- 连接管理由调用方负责，提高整体性能

		查询逻辑：
		- 使用image_filename在image_path列中进行LIKE匹配
		- 如果设置了data_source_filter，会添加额外的筛选条件
		- 查询结果限制为1条记录（假设图像文件名唯一）

		标签提取逻辑：
		- 支持多种标注数据格式（shapes、labels、annotations字段）
		- 保持标签原始顺序，不进行去重处理
		- 对异常数据进行容错处理

		Args:
			image_filename (str): 图像文件名（不含路径），用于数据库查询匹配
			client (PostgreSQLClient): 外部传入的数据库连接实例

		Returns:
			Tuple[List[str], str]: (标签列表, 图像文件路径)
			- 标签列表: 从数据库标注数据中提取的标签列表（保持原始顺序）
			- 图像文件路径: 数据库中存储的完整图像路径

		Raises:
			ValueError: 当数据库配置不正确或查询失败时抛出异常

		使用示例：
			# 创建数据库连接
			client = PostgreSQLClient(host="localhost", database="db", user="user", password="pwd")
			try:
				labels, image_path = self.__extract_labels_from_database("image001.jpg", client)
				print(f"提取到标签: {labels}")
				print(f"图像路径: {image_path}")
			finally:
				client.close()
		"""
		try:
			# 1. 获取数据库配置信息
			database_name = self.__get_database_config_value( "database_name_input" )
			table_name = self.__get_database_config_value( "table_name_input" )
			data_source_filter = self.__get_database_config_value( "data_source_filter_input", required=False )

			if not database_name or not table_name:
				raise ValueError( "数据库名称和表名不能为空" )

			# 2. 确定查询的数据列（根据checkbox状态）
			query_column = self.__determine_query_column()
			if not query_column:
				raise ValueError( "必须选择segment模式或obb模式中的至少一种" )

			# 3. 构建查询条件
			base_condition = f"image_id == '{image_filename}'"
			if data_source_filter:
				base_condition += f" AND data_source == '{data_source_filter}'"

			# 4. 执行数据库查询（使用外部传入的连接）
			try:
				if self.__logger:
					self.__logger.info( f"查询数据库: 表={table_name}, 列={query_column}, 条件={base_condition}" )

				result = client.fetch_data(
					table_name=table_name,
					columns=[ "image_path", query_column ],
					condition_str=base_condition,
					limit=1
				)

				if self.__logger:
					self.__logger.debug( f"数据库查询完成，返回 {len( result ) if result else 0} 条记录" )

			except Exception as query_error:
				error_msg = f"数据库查询失败: {str( query_error )}"
				if self.__logger:
					self.__logger.error( error_msg )
				if self.__log_output:
					self.__log_output.append( error_msg, color="red" )
				raise ValueError( error_msg ) from query_error

			# 5. 处理查询结果
			if not result:
				if self.__logger:
					self.__logger.warning( f"数据库中未找到图像文件: {image_filename}" )
				return [ ], ""

			# 6. 提取标签数据
			row = result.get( "data", [ ] )[ 0 ]
			image_path = row.get( "image_path", "" )
			annotation_data = row.get( query_column, { } )

			# 7. 从标注数据中提取标签
			labels = self.__extract_labels_from_annotation_data( annotation_data )

			if self.__logger:
				self.__logger.info( f"从数据库提取到 {len( labels )} 个标签: {labels}" )

			return labels, image_path

		except Exception as e:
			error_msg = f"数据库查询失败 [{image_filename}]: {str( e )}"
			if self.__logger:
				self.__logger.error( error_msg )
			if self.__log_output:
				self.__log_output.append( error_msg, color="red" )
			raise ValueError( error_msg ) from e

	def __get_database_config_value( self, config_key: str, required: bool = True ) -> str:
		"""
		获取数据库配置值

		此方法从UI控件中获取数据库相关的配置信息，支持：
		- database_name_input: 数据库名称（必需）
		- table_name_input: 数据库表名（必需）
		- data_source_filter_input: 数据源筛选条件（可选）

		配置验证：
		- 检查配置键是否存在于__UI_WIDGET_CONFIG中
		- 验证对应的UI控件是否可用
		- 对必需配置进行空值检查

		Args:
			config_key (str): 配置键名，应为__UI_WIDGET_CONFIG中定义的键
			required (bool): 是否为必需配置，默认True

		Returns:
			str: 从UI控件获取的配置值，已去除首尾空格

		Raises:
			ValueError: 当必需配置为空或配置键不存在时抛出异常

		使用示例：
			database_name = self.__get_database_config_value("database_name_input")
			table_name = self.__get_database_config_value("table_name_input")
			data_source = self.__get_database_config_value("data_source_filter_input", required=False)
		"""
		try:
			widget_config = self.__UI_WIDGET_CONFIG[ "input_controls" ].get( config_key )
			if not widget_config:
				raise ValueError( f"未找到配置项: {config_key}" )

			widget_id = widget_config[ "widget_id" ]
			manager_type = widget_config[ "manager_type" ]

			# 根据管理器类型获取值
			if manager_type == "LineEditManager" and self.__line_edit_manager:
				value = self.__line_edit_manager.get_text( widget_id )
			else:
				raise ValueError( f"不支持的管理器类型或管理器未初始化: {manager_type}" )

			# 验证必需值
			if required and not value:
				raise ValueError( f"必需的配置项不能为空: {config_key}" )

			return value or ""

		except Exception as e:
			error_msg = f"获取数据库配置失败 [{config_key}]: {str( e )}"
			if self.__logger:
				self.__logger.error( error_msg )
			raise ValueError( error_msg ) from e

	def __determine_query_column( self ) -> str:
		"""
		根据checkbox状态确定要查询的数据列

		此方法根据UI中的checkbox选择状态来决定查询数据库的哪个列：
		- rotate_146 (segment模式checkbox): 对应segmentation_data列
		- rotate_147 (obb模式checkbox): 对应obb_data列

		选择逻辑：
		- 如果只选择了segment模式，返回"segmentation_data"
		- 如果只选择了obb模式，返回"obb_data"
		- 如果两个都选择了，优先返回"segmentation_data"
		- 如果两个都没选择，抛出异常

		这种设计确保了数据库查询的明确性和一致性。

		Returns:
			str: 查询列名 ("segmentation_data" 或 "obb_data")

		Raises:
			ValueError: 当checkbox状态配置错误或CheckBoxManager未初始化时抛出异常

		使用示例：
			query_column = self.__determine_query_column()
			print(f"将查询数据库列: {query_column}")
		"""
		try:
			if not self.__checkbox_manager:
				raise ValueError( "CheckBoxManager未初始化" )

			# 获取checkbox状态
			segment_checked = self.__checkbox_manager.get_checked_state_by_object_name( "rotate_146" )
			obb_checked = self.__checkbox_manager.get_checked_state_by_object_name( "rotate_147" )

			# 验证checkbox状态
			if segment_checked is None or obb_checked is None:
				raise ValueError( "无法获取checkbox状态，请检查控件配置" )

			# 确定查询列（优先选择segment模式）
			if segment_checked:
				return "segmentation_data"
			elif obb_checked:
				return "obb_data"
			else:
				return ""

		except Exception as e:
			error_msg = f"确定查询列失败: {str( e )}"
			if self.__logger:
				self.__logger.error( error_msg )
			raise ValueError( error_msg ) from e

	def __extract_labels_from_annotation_data( self, annotation_data: Union[ Dict[ str, Any ], List[ Dict ] ] ) -> List[ str ]:
		"""
		从标注数据中提取标签列表

		此方法支持多种标注数据格式的标签提取：
		1. shapes格式: 包含shapes数组，每个shape有label字段
		2. labels格式: 直接包含labels数组
		3. annotations格式: 包含annotations数组，每个annotation有label字段
		4. 直接列表格式: 标注对象列表，每个对象直接包含label字段

		提取逻辑：
		- 遍历所有可能的数据结构
		- 提取所有找到的标签
		- 保持标签原始顺序，不进行去重处理
		- 对异常数据进行容错处理

		支持的数据结构示例：
		格式1 - 字典包含shapes数组：
		{
			"shapes": [
				{"label": "主任务", ...},
				{"label": "XXX", ...}
			]
		}

		格式2 - 字典包含labels数组：
		{
			"labels": ["主任务", "XXX", "YYY"]
		}

		格式3 - 字典包含annotations数组：
		{
			"annotations": [
				{"label": "主任务", ...},
				{"label": "XXX", ...}
			]
		}

		格式4 - 直接标注对象列表（新增支持）：
		[
			{
				"label": "亚基矿",
				"points": [[454.73, 363.76], [455.47, 275.71], ...],
				"shape_type": "rotation",
				"score": 0.91455,
				...
			},
			{
				"label": "主任务",
				"points": [[100, 200], [150, 250], ...],
				"shape_type": "rectangle",
				...
			}
		]

		Args:
			annotation_data (Union[Dict[str, Any], List[Dict]]): 从数据库查询得到的标注数据
				- Dict格式: 包含shapes/labels/annotations字段的字典
				- List格式: 直接的标注对象列表，每个对象包含label字段

		Returns:
			List[str]: 标签列表（保持原始顺序，不去重），如果没有找到标签则返回空列表

		使用示例：
			# 字典格式
			annotation_data = {"shapes": [{"label": "主任务"}, {"label": "XXX"}]}
			labels = self.__extract_labels_from_annotation_data(annotation_data)
			print(f"提取到的标签: {labels}")  # ['主任务', 'XXX']

			# 列表格式（新增支持）
			annotation_data = [
				{"label": "亚基矿", "points": [[454.73, 363.76]], "shape_type": "rotation"},
				{"label": "主任务", "points": [[100, 200]], "shape_type": "rectangle"}
			]
			labels = self.__extract_labels_from_annotation_data(annotation_data)
			print(f"提取到的标签: {labels}")  # ['亚基矿', '主任务']
		"""
		try:
			labels = [ ]

			# 处理不同格式的标注数据
			if isinstance( annotation_data, dict ):
				# 检查是否有shapes字段（类似JSON格式）
				if 'shapes' in annotation_data and isinstance( annotation_data[ 'shapes' ], list ):
					for shape in annotation_data[ 'shapes' ]:
						if isinstance( shape, dict ) and 'label' in shape:
							labels.append( shape[ 'label' ] )
				# 检查是否直接包含标签信息
				elif 'labels' in annotation_data and isinstance( annotation_data[ 'labels' ], list ):
					labels.extend( annotation_data[ 'labels' ] )
				# 检查其他可能的标签字段
				elif 'annotations' in annotation_data and isinstance( annotation_data[ 'annotations' ], list ):
					for annotation in annotation_data[ 'annotations' ]:
						if isinstance( annotation, dict ) and 'label' in annotation:
							labels.append( annotation[ 'label' ] )

			# 处理新的直接列表格式（List[Dict]）
			elif isinstance( annotation_data, list ):
				for annotation_obj in annotation_data:
					if isinstance( annotation_obj, dict ) and 'label' in annotation_obj:
						# 提取label字段值
						label_value = annotation_obj[ 'label' ]
						if label_value:  # 确保标签值不为空
							labels.append( label_value )

			# 直接返回标签列表（不去重）
			return labels

		except Exception as e:
			if self.__logger:
				self.__logger.warning( f"提取标签时发生错误: {str( e )}" )
			return [ ]

	def execute_database_batch_deletion( self, folder_path: str = None, condition_expression: str = None ) -> Dict[
		str, Any ]:
		"""
		执行基于数据库查询的批量删除操作

		这是新的主要公共方法，执行基于数据库的批量删除流程：
		1. 验证数据库配置和条件表达式参数
		2. 扫描文件夹中的图像文件
		3. 对每个图像文件查询数据库获取标签信息
		4. 解析条件表达式并评估
		5. 删除满足条件的文件
		6. 生成总结报告

		Args:
			folder_path (str, optional): 文件夹路径，如果不提供则从UI控件获取
			condition_expression (str, optional): 条件表达式，如果不提供则从UI控件获取

		Returns:
			Dict[str, Any]: 操作结果字典，包含统计信息和状态

		使用示例：
			# 基本使用（从UI控件获取参数）
			result = deleter.execute_database_batch_deletion()

			# 指定参数使用
			result = deleter.execute_database_batch_deletion(
				folder_path="/path/to/image/files",
				condition_expression="主任务 >= 1"
			)

			# 复杂条件表达式示例
			result = deleter.execute_database_batch_deletion(
				folder_path="/path/to/image/files",
				condition_expression="(主任务 >= 1 AND XXX >= 1) OR YYY >= 2"
			)

			# 检查结果
			if result["success"]:
				print(f"成功删除 {result['deleted_image_files']} 个图像文件")
				print(f"成功删除 {result['deleted_json_files']} 个JSON文件")
				print(f"匹配条件的文件数: {result['matched_files']}")
			else:
				print(f"操作失败: {result['error_message']}")

		数据库配置要求：
			在使用此方法前，需要确保以下UI控件已正确配置：
			- rotate_146: segment模式选择checkbox
			- rotate_147: obb模式选择checkbox
			- lineEdit_174: 数据库名称输入框
			- lineEdit_175: 数据库表名输入框
			- lineEdit_176: 数据源筛选条件输入框（可选）

		数据库连接配置：
			- 主机: localhost
			- 端口: 5432
			- 用户: postgres
			- 密码: 123456

		支持的数据库表结构：
			表中应包含以下列：
			- image_path: 图像文件路径
			- segmentation_data: 分割标注数据（JSONB格式）
			- obb_data: OBB标注数据（JSONB格式）
			- data_source: 数据源标识（可选，用于筛选）
		"""
		# 初始化数据库连接变量
		db_client = None

		try:
			# 重置统计信息
			self.__reset_stats()

			if self.__logger:
				self.__logger.info( "开始执行基于数据库的批量删除操作", color="blue" )
			if self.__log_output:
				self.__log_output.append( "开始执行基于数据库的批量删除操作", color="blue" )

			# 1. 验证数据库配置
			try:
				database_name = self.__get_database_config_value( "database_name_input" )
				table_name = self.__get_database_config_value( "table_name_input" )
				query_column = self.__determine_query_column()

				if self.__logger:
					self.__logger.info( f"数据库配置: 数据库={database_name}, 表={table_name}, 查询列={query_column}" )
				if self.__log_output:
					self.__log_output.append(
						f"数据库配置: 数据库={database_name}, 表={table_name}, 查询列={query_column}"
					)

			except Exception as e:
				error_msg = f"数据库配置验证失败: {str( e )}"
				if self.__logger:
					self.__logger.error( error_msg )
				if self.__log_output:
					self.__log_output.append( error_msg, color="red" )
				return { "success": False, "error_message": error_msg }

			# 2. 创建数据库连接（优化：整个批量删除过程只创建一次）
			try:
				db_client = PostgreSQLClient(
					host="localhost",
					port=5432,
					database=database_name,
					user="postgres",
					password="123456"
				)
				if self.__logger:
					self.__logger.info( f"成功建立数据库连接: {database_name}" )
				if self.__log_output:
					self.__log_output.append( f"成功建立数据库连接: {database_name}" )
			except Exception as conn_error:
				error_msg = f"数据库连接失败: {str( conn_error )}"
				if self.__logger:
					self.__logger.error( error_msg )
				if self.__log_output:
					self.__log_output.append( error_msg, color="red" )
				return { "success": False, "error_message": error_msg }

			# 2. 获取文件夹路径
			if folder_path is None:
				try:
					folder_path = self.__get_input_value( "folder_path_input" )
				except Exception as e:
					error_msg = f"无法获取文件夹路径: {str( e )}"
					if self.__logger:
						self.__logger.error( error_msg )
					if self.__log_output:
						self.__log_output.append( error_msg, color="red" )
					return { "success": False, "error_message": error_msg }

			if not folder_path or not folder_path.strip():
				error_msg = "文件夹路径不能为空"
				if self.__logger:
					self.__logger.error( error_msg )
				if self.__log_output:
					self.__log_output.append( error_msg, color="red" )
				return { "success": False, "error_message": error_msg }

			folder_path = folder_path.strip()
			if self.__logger:
				self.__logger.info( f"目标文件夹: {folder_path}" )
			if self.__log_output:
				self.__log_output.append( f"目标文件夹: {folder_path}" )

			# 3. 获取条件表达式
			if condition_expression is None:
				try:
					condition_expression = self.__get_input_value( "condition_expression_input" )
				except Exception as e:
					error_msg = f"无法获取条件表达式: {str( e )}"
					if self.__logger:
						self.__logger.error( error_msg )
					if self.__log_output:
						self.__log_output.append( error_msg, color="red" )
					return { "success": False, "error_message": error_msg }

			if not condition_expression or not condition_expression.strip():
				error_msg = "条件表达式不能为空"
				if self.__logger:
					self.__logger.error( error_msg )
				if self.__log_output:
					self.__log_output.append( error_msg, color="red" )
				return { "success": False, "error_message": error_msg }

			condition_expression = condition_expression.strip()
			if self.__logger:
				self.__logger.info( f"条件表达式: {condition_expression}" )
			if self.__log_output:
				self.__log_output.append( f"条件表达式: {condition_expression}" )

			# 4. 扫描JSON文件并生成对应的图像文件名列表
			try:
				image_files = self.__scan_json_files_for_database_mode( folder_path )
				self.__stats[ "scanned_files" ] = len( image_files )

				if not image_files:
					warning_msg = "未找到任何JSON文件"
					if self.__logger:
						self.__logger.warning( warning_msg )
					if self.__log_output:
						self.__log_output.append( warning_msg, color="orange" )
					return { "success": True, "warning_message": warning_msg, **self.__stats }

			except Exception as e:
				error_msg = f"扫描JSON文件失败: {str( e )}"
				if self.__logger:
					self.__logger.error( error_msg )
				if self.__log_output:
					self.__log_output.append( error_msg, color="red" )
				return { "success": False, "error_message": error_msg }

			# 5. 解析条件表达式
			try:
				condition_data = self.__parse_condition_expression( condition_expression )
			except Exception as e:
				error_msg = f"解析条件表达式失败: {str( e )}"
				if self.__logger:
					self.__logger.error( error_msg )
				if self.__log_output:
					self.__log_output.append( error_msg, color="red" )
				return { "success": False, "error_message": error_msg }

			# 6. 逐个处理图像文件名
			if self.__logger:
				self.__logger.info( "开始查询数据库并执行删除操作", color="yellow" )
				self.__logger.info( f"总共需要处理 {len( image_files )} 个图像文件名" )
			if self.__log_output:
				self.__log_output.append( "开始查询数据库并执行删除操作", color="yellow" )
				self.__log_output.append( f"总共需要处理 {len( image_files )} 个图像文件名" )

			processed_count = 0
			for image_filename in image_files:
				processed_count += 1
				try:
					# 显示进度信息
					if processed_count % 10 == 1 or processed_count == len( image_files ):
						progress_msg = f"正在处理第 {processed_count}/{len( image_files )} 个文件"
						if self.__logger:
							self.__logger.info( progress_msg )
						if self.__log_output:
							self.__log_output.append( progress_msg )

					# 从数据库提取标签信息（使用共享的数据库连接）
					if self.__logger:
						self.__logger.debug( f"查询图像文件: {image_filename}" )

					labels, _ = self.__extract_labels_from_database( image_filename, db_client )

					# 评估条件
					if self.__evaluate_condition( labels, condition_data ):
						self.__stats[ "matched_files" ] += 1

						# 构建完整的图像文件路径
						image_file_path = os.path.join( folder_path, image_filename )

						# 查找对应的JSON文件
						json_file_path = self.__find_corresponding_json_file( image_file_path )

						# 删除文件
						json_deleted, image_deleted = self.__delete_files_safely( json_file_path, image_file_path )

						if json_deleted:
							self.__stats[ "deleted_json_files" ] += 1
						if image_deleted:
							self.__stats[ "deleted_image_files" ] += 1

						if not image_deleted or (json_file_path and not json_deleted):
							self.__stats[ "failed_deletions" ] += 1

				except Exception as e:
					if self.__logger:
						self.__logger.error( f"处理文件失败 [{image_filename}]: {str( e )}" )
					if self.__log_output:
						self.__log_output.append(
							f"处理文件失败 [{image_filename}]: {str( e )}", color="red"
						)
					self.__stats[ "errors" ].append(
						f"处理文件失败 [{image_filename}]: {str( e )}"
					)
					self.__stats[ "failed_deletions" ] += 1

			# 7. 生成总结报告
			summary_report = self.__generate_summary_report()
			if self.__logger:
				self.__logger.info( "操作完成", color="green" )
				self.__logger.info( summary_report, color="cyan" )
			if self.__log_output:
				self.__log_output.append( "操作完成", color="green" )
				self.__log_output.append( summary_report, color="cyan" )

			return { "success": True, **self.__stats, "summary_report": summary_report }

		except Exception as e:
			error_msg = f"批量删除操作失败: {str( e )}"
			if self.__logger:
				self.__logger.error( error_msg )
			if self.__log_output:
				self.__log_output.append( error_msg, color="red" )
			return { "success": False, "error_message": error_msg }

		finally:
			# 确保数据库连接在任何情况下都能正确关闭
			if db_client:
				try:
					db_client.close()
					if self.__logger:
						self.__logger.info( "数据库连接已关闭" )
					if self.__log_output:
						self.__log_output.append( "数据库连接已关闭" )
				except Exception as close_error:
					if self.__logger:
						self.__logger.warning( f"关闭数据库连接时发生警告: {str( close_error )}" )
					if self.__log_output:
						self.__log_output.append( f"关闭数据库连接时发生警告: {str( close_error )}", color="orange" )

	def __scan_image_files( self, folder_path: str ) -> List[ str ]:
		"""
		扫描文件夹中的图像文件

		Args:
			folder_path (str): 文件夹路径

		Returns:
			List[str]: 图像文件路径列表

		Raises:
			FileNotFoundError: 当文件夹不存在时抛出异常
			PermissionError: 当没有访问权限时抛出异常
		"""
		try:
			if not os.path.exists( folder_path ):
				raise FileNotFoundError( f"文件夹不存在: {folder_path}" )

			if not os.path.isdir( folder_path ):
				raise ValueError( f"路径不是文件夹: {folder_path}" )

			# 支持的图像文件扩展名
			image_extensions = { '.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.webp' }
			image_files = [ ]

			# 递归扫描文件夹
			for root, _, files in os.walk( folder_path ):
				for file in files:
					file_path = os.path.join( root, file )
					file_ext = os.path.splitext( file )[ 1 ].lower()

					if file_ext in image_extensions:
						image_files.append( file_path )

			if self.__logger:
				self.__logger.info( f"扫描到 {len( image_files )} 个图像文件" )
			if self.__log_output:
				self.__log_output.append( f"扫描到 {len( image_files )} 个图像文件" )

			return image_files

		except FileNotFoundError:
			if self.__logger:
				self.__logger.error( f"文件夹不存在: {folder_path}" )
			if self.__log_output:
				self.__log_output.append( f"文件夹不存在: {folder_path}", color="red" )
			raise
		except PermissionError:
			if self.__logger:
				self.__logger.error( f"没有访问文件夹的权限: {folder_path}" )
			if self.__log_output:
				self.__log_output.append( f"没有访问文件夹的权限: {folder_path}", color="red" )
			raise
		except Exception as e:
			if self.__logger:
				self.__logger.error( f"扫描图像文件时发生错误: {str( e )}" )
			if self.__log_output:
				self.__log_output.append( f"扫描图像文件时发生错误: {str( e )}", color="red" )
			raise

	def __scan_json_files_for_database_mode( self, folder_path: str ) -> List[ str ]:
		"""
		扫描文件夹中的JSON文件并生成对应的图像文件名列表（用于数据库模式）

		该方法专门为execute_database_batch_deletion方法设计：
		1. 扫描指定文件夹中的所有JSON文件
		2. 提取每个JSON文件的文件名（不包含路径和扩展名）
		3. 为每个JSON文件名添加".jpg"后缀
		4. 返回处理后的文件名列表

		Args:
			folder_path (str): 文件夹路径

		Returns:
			List[str]: 处理后的文件名列表，格式为["filename1.jpg", "filename2.jpg", ...]

		Raises:
			FileNotFoundError: 当文件夹不存在时抛出异常
			PermissionError: 当没有访问权限时抛出异常

		使用示例：
			# 假设文件夹中有以下JSON文件：
			# - /path/to/folder/image001.json
			# - /path/to/folder/image002.json
			# - /path/to/folder/subdir/image003.json

			processed_filenames = self.__scan_json_files_for_database_mode("/path/to/folder")
			# 返回结果：["image001.jpg", "image002.jpg", "image003.jpg"]
		"""
		try:
			if not os.path.exists( folder_path ):
				raise FileNotFoundError( f"文件夹不存在: {folder_path}" )

			if not os.path.isdir( folder_path ):
				raise ValueError( f"路径不是文件夹: {folder_path}" )

			processed_filenames = [ ]

			# 递归扫描文件夹中的JSON文件
			for _, _, files in os.walk( folder_path ):
				for file in files:
					file_ext = os.path.splitext( file )[ 1 ].lower()

					# 只处理JSON文件
					if file_ext == '.json':
						# 提取文件名（不包含扩展名）
						filename_without_ext = os.path.splitext( file )[ 0 ]
						# 添加.jpg后缀
						processed_filename = f"{filename_without_ext}.jpg"
						processed_filenames.append( processed_filename )

			if self.__logger:
				self.__logger.info( f"扫描到 {len( processed_filenames )} 个JSON文件，生成对应的图像文件名" )
			if self.__log_output:
				self.__log_output.append( f"扫描到 {len( processed_filenames )} 个JSON文件，生成对应的图像文件名" )

			return processed_filenames

		except FileNotFoundError:
			if self.__logger:
				self.__logger.error( f"文件夹不存在: {folder_path}" )
			if self.__log_output:
				self.__log_output.append( f"文件夹不存在: {folder_path}", color="red" )
			raise
		except PermissionError:
			if self.__logger:
				self.__logger.error( f"没有访问文件夹的权限: {folder_path}" )
			if self.__log_output:
				self.__log_output.append( f"没有访问文件夹的权限: {folder_path}", color="red" )
			raise
		except Exception as e:
			if self.__logger:
				self.__logger.error( f"扫描JSON文件时发生错误: {str( e )}" )
			if self.__log_output:
				self.__log_output.append( f"扫描JSON文件时发生错误: {str( e )}", color="red" )
			raise

	def __find_corresponding_json_file( self, image_file_path: str ) -> Optional[ str ]:
		"""
		查找图像文件对应的JSON标注文件

		Args:
			image_file_path (str): 图像文件路径

		Returns:
			Optional[str]: JSON文件路径，如果不存在则返回None
		"""
		try:
			# 获取图像文件的目录和基础名称
			image_dir = os.path.dirname( image_file_path )
			image_name = os.path.splitext( os.path.basename( image_file_path ) )[ 0 ]

			# 构建可能的JSON文件路径
			json_file_path = os.path.join( image_dir, f"{image_name}.json" )

			# 检查JSON文件是否存在
			if os.path.exists( json_file_path ):
				return json_file_path
			else:
				if self.__logger:
					self.__logger.warning( f"未找到对应的JSON文件: {json_file_path}" )
				return None

		except Exception as e:
			if self.__logger:
				self.__logger.warning( f"查找JSON文件时发生错误: {str( e )}" )
			return None

	def __tokenize_expression( self, expression: str ) -> List[ Token ]:
		"""
		对条件表达式进行词法分析

		Args:
			expression (str): 条件表达式

		Returns:
			List[Token]: 标记列表

		Raises:
			ValueError: 当表达式包含无法识别的字符时抛出异常
		"""
		try:
			tokenizer = ExpressionTokenizer( expression )
			return tokenizer.get_tokens()
		except Exception as e:
			raise ValueError( f"词法分析失败: {str( e )}" )

	def __parse_logical_expression( self, tokens: List[ Token ] ) -> ASTNode:
		"""
		递归下降解析器 - 解析逻辑表达式

		语法规则:
		expression := or_expression
		or_expression := and_expression ('OR' and_expression)*
		and_expression := not_expression ('AND' not_expression)*
		not_expression := 'NOT' not_expression | primary_expression
		primary_expression := '(' expression ')' | comparison_expression
		comparison_expression := LABEL OPERATOR NUMBER

		Args:
			tokens (List[Token]): 标记列表

		Returns:
			ASTNode: 抽象语法树根节点

		Raises:
			ValueError: 当语法错误时抛出异常
		"""
		self.__token_index = 0
		self.__tokens = tokens

		try:
			ast = self.__parse_or_expression()

			# 检查是否还有未处理的标记
			if self.__current_token().type != TokenType.EOF:
				raise ValueError( f"表达式解析不完整，位置 {self.__current_token().position} 处有多余的内容" )

			return ast
		except IndexError:
			raise ValueError( "表达式意外结束" )

	def __current_token( self ) -> Token:
		"""获取当前标记"""
		if self.__token_index < len( self.__tokens ):
			return self.__tokens[ self.__token_index ]
		return self.__tokens[ -1 ]  # EOF token

	def __consume_token( self, expected_type: TokenType = None ) -> Token:
		"""消费当前标记并移动到下一个"""
		token = self.__current_token()
		if expected_type and token.type != expected_type:
			raise ValueError( f"期望 {expected_type.value}，但得到 {token.type.value} 在位置 {token.position}" )

		if self.__token_index < len( self.__tokens ) - 1:
			self.__token_index += 1
		return token

	def __parse_or_expression( self ) -> ASTNode:
		"""解析 OR 表达式"""
		left = self.__parse_and_expression()

		while self.__current_token().type == TokenType.OR:
			self.__consume_token( TokenType.OR )
			right = self.__parse_and_expression()
			left = LogicalNode( "OR", left, right )

		return left

	def __parse_and_expression( self ) -> ASTNode:
		"""解析 AND 表达式"""
		left = self.__parse_not_expression()

		while self.__current_token().type == TokenType.AND:
			self.__consume_token( TokenType.AND )
			right = self.__parse_not_expression()
			left = LogicalNode( "AND", left, right )

		return left

	def __parse_not_expression( self ) -> ASTNode:
		"""解析 NOT 表达式"""
		if self.__current_token().type == TokenType.NOT:
			self.__consume_token( TokenType.NOT )
			operand = self.__parse_not_expression()
			return LogicalNode( "NOT", operand )
		else:
			return self.__parse_primary_expression()

	def __parse_primary_expression( self ) -> ASTNode:
		"""解析主表达式（括号或比较表达式）"""
		if self.__current_token().type == TokenType.LPAREN:
			self.__consume_token( TokenType.LPAREN )
			expr = self.__parse_or_expression()
			self.__consume_token( TokenType.RPAREN )
			return expr
		else:
			return self.__parse_comparison_expression()

	def __parse_comparison_expression( self ) -> ASTNode:
		"""解析比较表达式"""
		# 期望格式: LABEL OPERATOR NUMBER
		label_token = self.__consume_token( TokenType.LABEL )
		operator_token = self.__consume_token( TokenType.OPERATOR )
		number_token = self.__consume_token( TokenType.NUMBER )

		# 验证运算符
		if operator_token.value not in self.__OPERATORS:
			raise ValueError( f"不支持的运算符: {operator_token.value}" )

		# 转换数值
		try:
			if '.' in number_token.value:
				value = float( number_token.value )
			else:
				value = int( number_token.value )
		except ValueError:
			raise ValueError( f"无效的数值: {number_token.value}" )

		return ComparisonNode( label_token.value, operator_token.value, value )

	def __parse_condition_expression( self, expression: str ) -> Union[
		Tuple[ str, str, Union[ int, float ] ], ASTNode ]:
		"""
		解析条件表达式 - 支持简单和复杂表达式

		简单表达式示例: "主人物 >= 1"
		复杂表达式示例: "(主任务 >= 1 AND XXX >= 1) OR YYY >= 2"

		Args:
			expression (str): 条件表达式

		Returns:
			Union[Tuple[str, str, Union[int, float]], ASTNode]:
				简单表达式返回 (标签名, 运算符, 数值)
				复杂表达式返回 AST 节点

		Raises:
			ValueError: 当表达式格式错误时抛出异常
		"""
		expression = expression.strip()

		if not expression:
			raise ValueError( "条件表达式不能为空" )

		# 检查是否包含逻辑运算符或括号，判断是简单还是复杂表达式
		has_logical_operators = any( keyword in expression.upper() for keyword in [ 'AND', 'OR', 'NOT' ] )
		has_parentheses = '(' in expression or ')' in expression

		if has_logical_operators or has_parentheses:
			# 复杂表达式：使用递归下降解析器
			try:
				tokens = self.__tokenize_expression( expression )
				ast = self.__parse_logical_expression( tokens )

				if self.__logger:
					self.__logger.info( f"解析复杂条件表达式成功: {expression}" )
				if self.__log_output:
					self.__log_output.append( f"解析复杂条件表达式成功: {expression}" )

				return ast
			except Exception as e:
				raise ValueError( f"复杂表达式解析失败: {str( e )}" )
		else:
			# 简单表达式：使用原有的正则表达式解析（向后兼容）
			pattern = r'^(.+?)\s*(>=|<=|==|!=|>|<)\s*(\d+(?:\.\d+)?)$'
			match = re.match( pattern, expression )

			if not match:
				raise ValueError( f"条件表达式格式错误: {expression}。正确格式示例: '主人物 >= 1'" )

			label_name = match.group( 1 ).strip()
			operator_str = match.group( 2 )
			value_str = match.group( 3 )

			# 验证运算符
			if operator_str not in self.__OPERATORS:
				raise ValueError( f"不支持的运算符: {operator_str}" )

			# 转换数值
			try:
				if '.' in value_str:
					value = float( value_str )
				else:
					value = int( value_str )
			except ValueError:
				raise ValueError( f"无效的数值: {value_str}" )

			if self.__logger:
				self.__logger.info( f"解析简单条件表达式: 标签='{label_name}', 运算符='{operator_str}', 数值={value}" )
			if self.__log_output:
				self.__log_output.append(
					f"解析简单条件表达式: 标签='{label_name}', 运算符='{operator_str}', 数值={value}"
				)

			return label_name, operator_str, value

	def __evaluate_complex_condition( self, labels: List[ str ], ast_node: ASTNode ) -> bool:
		"""
		评估复杂条件表达式的AST节点

		Args:
			labels (List[str]): 标签列表
			ast_node (ASTNode): 抽象语法树节点

		Returns:
			bool: 条件是否满足
		"""
		if isinstance( ast_node, ComparisonNode ):
			# 比较节点：直接评估
			return self.__evaluate_simple_condition( labels, ast_node.label_name, ast_node.operator, ast_node.value )

		elif isinstance( ast_node, LogicalNode ):
			# 逻辑节点：根据运算符递归评估
			if ast_node.operator == "AND":
				left_result = self.__evaluate_complex_condition( labels, ast_node.left )
				right_result = self.__evaluate_complex_condition( labels, ast_node.right )
				result = left_result and right_result

				if self.__logger:
					self.__logger.info( f"逻辑AND评估: {left_result} AND {right_result} = {result}" )
				if self.__log_output:
					self.__log_output.append( f"逻辑AND评估: {left_result} AND {right_result} = {result}" )

				return result

			elif ast_node.operator == "OR":
				left_result = self.__evaluate_complex_condition( labels, ast_node.left )
				right_result = self.__evaluate_complex_condition( labels, ast_node.right )
				result = left_result or right_result

				if self.__logger:
					self.__logger.info( f"逻辑OR评估: {left_result} OR {right_result} = {result}" )
				if self.__log_output:
					self.__log_output.append( f"逻辑OR评估: {left_result} OR {right_result} = {result}" )

				return result

			elif ast_node.operator == "NOT":
				operand_result = self.__evaluate_complex_condition( labels, ast_node.left )
				result = not operand_result

				if self.__logger:
					self.__logger.info( f"逻辑NOT评估: NOT {operand_result} = {result}" )
				if self.__log_output:
					self.__log_output.append( f"逻辑NOT评估: NOT {operand_result} = {result}" )

				return result
			else:
				raise ValueError( f"不支持的逻辑运算符: {ast_node.operator}" )
		else:
			raise ValueError( f"不支持的AST节点类型: {type( ast_node )}" )

	def __evaluate_simple_condition(
			self, labels: List[ str ], label_name: str, operator_str: str, value: Union[ int, float ]
	) -> bool:
		"""
		评估简单条件表达式

		Args:
			labels (List[str]): 标签列表
			label_name (str): 要检查的标签名
			operator_str (str): 运算符
			value (Union[int, float]): 比较值

		Returns:
			bool: 条件是否满足
		"""
		# 统计指定标签的数量
		label_count = labels.count( label_name )

		# 获取运算符函数
		operator_func = self.__OPERATORS[ operator_str ]

		# 执行比较
		result = operator_func( label_count, value )

		if self.__logger:
			self.__logger.info(
				f"条件评估: '{label_name}' 数量={label_count}, {label_count} {operator_str} {value} = {result}"
			)
		if self.__log_output:
			self.__log_output.append(
				f"条件评估: '{label_name}' 数量={label_count}, {label_count} {operator_str} {value} = {result}"
			)

		return result

	def __evaluate_condition(
			self, labels: List[ str ], condition_data: Union[ Tuple[ str, str, Union[ int, float ] ], ASTNode ]
	) -> bool:
		"""
		评估条件表达式是否满足 - 支持简单和复杂条件

		Args:
			labels (List[str]): 标签列表
			condition_data: 条件数据，可以是简单条件的元组或复杂条件的AST节点

		Returns:
			bool: 条件是否满足
		"""
		if isinstance( condition_data, tuple ):
			# 简单条件：(标签名, 运算符, 数值)
			label_name, operator_str, value = condition_data
			return self.__evaluate_simple_condition( labels, label_name, operator_str, value )
		elif isinstance( condition_data, ASTNode ):
			# 复杂条件：AST节点
			return self.__evaluate_complex_condition( labels, condition_data )
		else:
			raise ValueError( f"不支持的条件数据类型: {type( condition_data )}" )

	def __delete_files_safely( self, json_file_path: str, image_file_path: str ) -> Tuple[ bool, bool ]:
		"""
		安全地删除JSON文件和对应的图像文件

		Args:
			json_file_path (str): JSON文件路径
			image_file_path (str): 图像文件路径

		Returns:
			Tuple[bool, bool]: (JSON文件删除成功, 图像文件删除成功)
		"""
		json_deleted = False
		image_deleted = False

		# 删除JSON文件
		try:
			if os.path.exists( json_file_path ):
				os.remove( json_file_path )
				json_deleted = True
				if self.__logger:
					self.__logger.info( f"已删除JSON文件: {os.path.basename( json_file_path )}", color="green" )
				if self.__log_output:
					self.__log_output.append( f"已删除JSON文件: {os.path.basename( json_file_path )}", color="green" )
			else:
				if self.__logger:
					self.__logger.warning( f"JSON文件不存在: {json_file_path}" )
				if self.__log_output:
					self.__log_output.append( f"JSON文件不存在: {json_file_path}", color="orange" )
		except Exception as e:
			if self.__logger:
				self.__logger.error( f"删除JSON文件失败 [{json_file_path}]: {str( e )}" )
			if self.__log_output:
				self.__log_output.append( f"删除JSON文件失败 [{json_file_path}]: {str( e )}", color="red" )
			self.__stats[ "errors" ].append( f"删除JSON文件失败: {str( e )}" )

		# 删除图像文件
		try:
			if image_file_path and os.path.exists( image_file_path ):
				os.remove( image_file_path )
				image_deleted = True
				if self.__logger:
					self.__logger.info( f"已删除图像文件: {os.path.basename( image_file_path )}", color="green" )
				if self.__log_output:
					self.__log_output.append( f"已删除图像文件: {os.path.basename( image_file_path )}", color="green" )
			elif image_file_path:
				if self.__logger:
					self.__logger.warning( f"图像文件不存在: {image_file_path}" )
				if self.__log_output:
					self.__log_output.append( f"图像文件不存在: {image_file_path}", color="orange" )
		except Exception as e:
			if self.__logger:
				self.__logger.error( f"删除图像文件失败 [{image_file_path}]: {str( e )}" )
			if self.__log_output:
				self.__log_output.append( f"删除图像文件失败 [{image_file_path}]: {str( e )}", color="red" )
			self.__stats[ "errors" ].append( f"删除图像文件失败: {str( e )}" )

		return json_deleted, image_deleted

	def __reset_stats( self ) -> None:
		"""重置统计信息"""
		self.__stats = {
			"scanned_files":       0,
			"matched_files":       0,
			"deleted_json_files":  0,
			"deleted_image_files": 0,
			"failed_deletions":    0,
			"errors":              [ ]
		}

	def __generate_summary_report( self ) -> str:
		"""
		生成操作总结报告

		Returns:
			str: 格式化的总结报告
		"""
		report_lines = [
			"=" * 50,
			"批量删除操作总结报告",
			"=" * 50,
			f"扫描文件数量: {self.__stats[ 'scanned_files' ]}",
			f"匹配条件文件: {self.__stats[ 'matched_files' ]}",
			f"删除JSON文件: {self.__stats[ 'deleted_json_files' ]}",
			f"删除图像文件: {self.__stats[ 'deleted_image_files' ]}",
			f"删除失败次数: {self.__stats[ 'failed_deletions' ]}",
			f"错误数量: {len( self.__stats[ 'errors' ] )}",
		]

		if self.__stats[ "errors" ]:
			report_lines.append( "\n错误详情:" )
			for i, error in enumerate( self.__stats[ "errors" ], 1 ):
				report_lines.append( f"  {i}. {error}" )

		report_lines.append( "=" * 50 )
		return "\n".join( report_lines )

	def execute_batch_deletion( self, folder_path: str = None, condition_expression: str = None ) -> Dict[ str, Any ]:
		"""
		执行批量删除操作

		这是主要的公共方法，执行完整的批量删除流程：
		1. 验证文件夹路径和条件表达式参数
		2. 扫描JSON文件
		3. 解析条件表达式（支持简单和复杂表达式）
		4. 逐个检查文件并删除满足条件的文件
		5. 生成总结报告

		Args:
			folder_path (str, optional): 文件夹路径，如果不提供则从UI控件获取
			condition_expression (str, optional): 条件表达式，如果不提供则从UI控件获取。
												支持简单表达式如'主任务>=1'或复杂表达式如'(主任务>=1 AND XXX>=1) OR YYY>=2'

		Returns:
			Dict[str, Any]: 操作结果字典，包含统计信息和状态

		使用示例：
			# 简单表达式
			result = deleter.execute_batch_deletion(
				folder_path="/path/to/json/files",
				condition_expression="主任务 >= 1"
			)

			# 复杂表达式
			result = deleter.execute_batch_deletion(
				folder_path="/path/to/json/files",
				condition_expression="主人物 >= 1"
			)

			# 检查结果
			if result["success"]:
				print(f"成功删除 {result['deleted_json_files']} 个JSON文件")
			else:
				print(f"操作失败: {result['error_message']}")
		"""
		try:
			# 重置统计信息
			self.__reset_stats()

			if self.__logger:
				self.__logger.info( "开始执行批量删除操作", color="blue" )
			if self.__log_output:
				self.__log_output.append( "开始执行批量删除操作", color="blue" )

			# 1. 获取文件夹路径
			if folder_path is None:
				try:
					folder_path = self.__get_input_value( "folder_path_input" )
				except Exception as e:
					if self.__logger:
						self.__logger.error( f"无法获取文件夹路径: {str( e )}" )
					if self.__log_output:
						self.__log_output.append( f"无法获取文件夹路径: {str( e )}", color="red" )
					return { "success": False, "error_message": f"无法获取文件夹路径: {str( e )}" }

			if not folder_path or not folder_path.strip():
				error_msg = "文件夹路径不能为空"
				if self.__logger:
					self.__logger.error( error_msg )
				if self.__log_output:
					self.__log_output.append( error_msg, color="red" )
				return { "success": False, "error_message": error_msg }

			folder_path = folder_path.strip()
			if self.__logger:
				self.__logger.info( f"目标文件夹: {folder_path}" )
			if self.__log_output:
				self.__log_output.append( f"目标文件夹: {folder_path}" )

			# 2. 获取条件表达式
			if condition_expression is None:
				try:
					condition_expression = self.__get_input_value( "condition_expression_input" )
				except Exception as e:
					if self.__logger:
						self.__logger.error( f"无法获取条件表达式: {str( e )}" )
					if self.__log_output:
						self.__log_output.append( f"无法获取条件表达式: {str( e )}", color="red" )
					return { "success": False, "error_message": f"无法获取条件表达式: {str( e )}" }

			if not condition_expression or not condition_expression.strip():
				error_msg = "条件表达式不能为空"
				if self.__logger:
					self.__logger.error( error_msg )
				if self.__log_output:
					self.__log_output.append( error_msg, color="red" )
				return { "success": False, "error_message": error_msg }

			condition_expression = condition_expression.strip()
			if self.__logger:
				self.__logger.info( f"条件表达式: {condition_expression}" )
			if self.__log_output:
				self.__log_output.append( f"条件表达式: {condition_expression}" )

			# 3. 扫描JSON文件
			try:
				json_files = self.__scan_json_files( folder_path )
				self.__stats[ "scanned_files" ] = len( json_files )

				if not json_files:
					warning_msg = "未找到任何JSON文件"
					if self.__logger:
						self.__logger.warning( warning_msg )
					if self.__log_output:
						self.__log_output.append( warning_msg, color="orange" )
					return { "success": True, "warning_message": warning_msg, **self.__stats }

			except Exception as e:
				error_msg = f"扫描文件失败: {str( e )}"
				if self.__logger:
					self.__logger.error( error_msg )
				if self.__log_output:
					self.__log_output.append( error_msg, color="red" )
				return { "success": False, "error_message": error_msg }

			# 4. 解析条件表达式
			try:
				condition_data = self.__parse_condition_expression( condition_expression )
			except Exception as e:
				error_msg = f"解析条件表达式失败: {str( e )}"
				if self.__logger:
					self.__logger.error( error_msg )
				if self.__log_output:
					self.__log_output.append( error_msg, color="red" )
				return { "success": False, "error_message": error_msg }

			# 5. 逐个处理JSON文件
			if self.__logger:
				self.__logger.info( "开始检查文件并执行删除操作", color="yellow" )
			if self.__log_output:
				self.__log_output.append( "开始检查文件并执行删除操作", color="yellow" )

			for json_file_path in json_files:
				try:
					# 提取标签和图像路径
					labels, image_path = self.__extract_labels_from_json( json_file_path )

					# 评估条件
					if self.__evaluate_condition( labels, condition_data ):
						self.__stats[ "matched_files" ] += 1

						# 删除文件
						json_deleted, image_deleted = self.__delete_files_safely( json_file_path, image_path )

						if json_deleted:
							self.__stats[ "deleted_json_files" ] += 1
						if image_deleted:
							self.__stats[ "deleted_image_files" ] += 1

						if not json_deleted or (image_path and not image_deleted):
							self.__stats[ "failed_deletions" ] += 1

				except Exception as e:
					if self.__logger:
						self.__logger.error( f"处理文件失败 [{os.path.basename( json_file_path )}]: {str( e )}" )
					if self.__log_output:
						self.__log_output.append(
							f"处理文件失败 [{os.path.basename( json_file_path )}]: {str( e )}", color="red"
						)
					self.__stats[ "errors" ].append(
						f"处理文件失败 [{os.path.basename( json_file_path )}]: {str( e )}"
					)
					self.__stats[ "failed_deletions" ] += 1

			# 6. 生成总结报告
			summary_report = self.__generate_summary_report()
			if self.__logger:
				self.__logger.info( "操作完成", color="green" )
				self.__logger.info( summary_report, color="cyan" )
			if self.__log_output:
				self.__log_output.append( "操作完成", color="green" )
				self.__log_output.append( summary_report, color="cyan" )

			# 7. 将报告输出到UI
			try:
				self.__set_output_value( "result_display", summary_report )
			except Exception as e:
				if self.__logger:
					self.__logger.warning( f"无法将报告输出到UI: {str( e )}" )
				if self.__log_output:
					self.__log_output.append( f"无法将报告输出到UI: {str( e )}", color="orange" )

			# 8. 返回结果
			return {
				"success":        True,
				"summary_report": summary_report,
				**self.__stats
			}

		except Exception as e:
			error_msg = f"批量删除操作发生未预期错误: {str( e )}"
			if self.__logger:
				self.__logger.error( error_msg )
			if self.__log_output:
				self.__log_output.append( error_msg, color="red" )
			return { "success": False, "error_message": error_msg }

	def get_stats( self ) -> Dict[ str, Any ]:
		"""
		获取当前统计信息

		Returns:
			Dict[str, Any]: 统计信息字典

		使用示例：
			stats = deleter.get_stats()
			print(f"已扫描 {stats['scanned_files']} 个文件")
		"""
		return self.__stats.copy()

	def validate_condition_expression( self, expression: str ) -> Tuple[ bool, str ]:
		"""
		验证条件表达式的格式是否正确

		Args:
			expression (str): 要验证的条件表达式

		Returns:
			Tuple[bool, str]: (是否有效, 错误消息或成功消息)

		使用示例：
			is_valid, message = deleter.validate_condition_expression("主人物 >= 1")
			if is_valid:
				print("表达式格式正确")
			else:
				print(f"表达式格式错误: {message}")
		"""
		try:
			condition_data = self.__parse_condition_expression( expression )
			if isinstance( condition_data, tuple ):
				label_name, operator_str, value = condition_data
				return True, f"简单表达式有效: 标签='{label_name}', 运算符='{operator_str}', 数值={value}"
			else:
				return True, f"复杂表达式有效: {expression}"
		except Exception as e:
			return False, str( e )

	def preview_deletion( self, folder_path: str = None, condition_expression: str = None ) -> Dict[ str, Any ]:
		"""
		预览删除操作，不实际删除文件

		Args:
			folder_path (str, optional): 文件夹路径，如果不提供则从UI控件获取
			condition_expression (str, optional): 条件表达式，如果不提供则从UI控件获取。
												支持简单表达式如'主任务>=1'或复杂表达式如'(主任务>=1 AND XXX>=1) OR YYY>=2'

		Returns:
			Dict[str, Any]: 预览结果，包含将要删除的文件列表

		使用示例：
			# 简单表达式预览
			preview = deleter.preview_deletion("/path/to/files", "主任务 >= 1")

			# 复杂表达式预览
			preview = deleter.preview_deletion("/path/to/files", "(主任务 >= 1 AND XXX >= 1) OR YYY >= 2")

			if preview["success"]:
				print(f"将删除 {len(preview['files_to_delete'])} 个文件")
				for file_info in preview['files_to_delete']:
					print(f"  - {file_info['json_file']}")
		"""
		try:
			# 获取参数
			if folder_path is None:
				folder_path = self.__get_input_value( "folder_path_input" )

			if condition_expression is None:
				condition_expression = self.__get_input_value( "condition_expression_input" )

			# 验证参数
			if not folder_path or not folder_path.strip():
				return { "success": False, "error_message": "文件夹路径不能为空" }
			if not condition_expression or not condition_expression.strip():
				return { "success": False, "error_message": "条件表达式不能为空" }

			# 扫描文件
			json_files = self.__scan_json_files( folder_path.strip() )

			# 解析条件
			condition_data = self.__parse_condition_expression( condition_expression.strip() )

			# 预览匹配的文件
			files_to_delete = [ ]
			for json_file_path in json_files:
				try:
					labels, image_path = self.__extract_labels_from_json( json_file_path )
					if self.__evaluate_condition( labels, condition_data ):
						# 为了兼容性，计算标签数量（对于复杂表达式可能不准确，但保持接口一致）
						if isinstance( condition_data, tuple ):
							label_name = condition_data[ 0 ]
							label_count = labels.count( label_name )
						else:
							label_name = "复杂表达式"
							label_count = len( labels )

						files_to_delete.append(
							{
								"json_file":   json_file_path,
								"image_file":  image_path,
								"labels":      labels,
								"label_count": label_count
							}
						)
				except Exception as e:
					if self.__logger:
						self.__logger.warning( f"预览时跳过文件 [{os.path.basename( json_file_path )}]: {str( e )}" )
					if self.__log_output:
						self.__log_output.append(
							f"预览时跳过文件 [{os.path.basename( json_file_path )}]: {str( e )}", color="orange"
						)

			# 构建条件描述
			if isinstance( condition_data, tuple ):
				label_name, operator_str, value = condition_data
				condition_desc = f"{label_name} {operator_str} {value}"
			else:
				condition_desc = condition_expression.strip()

			return {
				"success":         True,
				"total_files":     len( json_files ),
				"files_to_delete": files_to_delete,
				"condition":       condition_desc
			}

		except Exception as e:
			return { "success": False, "error_message": str( e ) }

	def update_widget_config( self, config_key: str, widget_id: str = None, **kwargs ) -> None:
		"""
		更新UI控件配置

		Args:
			config_key (str): 配置键名
			widget_id (str, optional): 新的控件ID
			**kwargs: 其他配置参数

		使用示例：
			# 更新文件夹路径输入框的控件ID
			deleter.update_widget_config("folder_path_input", widget_id="lineEdit_50")

			# 更新控件描述
			deleter.update_widget_config("result_display", description="新的描述")
		"""
		try:
			config = self.__get_widget_config( config_key )

			if widget_id is not None:
				config[ "widget_id" ] = widget_id
				if self.__logger:
					self.__logger.info( f"更新控件配置 [{config_key}] widget_id: {widget_id}" )
				if self.__log_output:
					self.__log_output.append( f"更新控件配置 [{config_key}] widget_id: {widget_id}" )

			for key, value in kwargs.items():
				if key in config:
					config[ key ] = value
					if self.__logger:
						self.__logger.info( f"更新控件配置 [{config_key}] {key}: {value}" )
					if self.__log_output:
						self.__log_output.append( f"更新控件配置 [{config_key}] {key}: {value}" )
				else:
					if self.__logger:
						self.__logger.warning( f"未知的配置参数: {key}" )
					if self.__log_output:
						self.__log_output.append( f"未知的配置参数: {key}", color="orange" )

		except Exception as e:
			if self.__logger:
				self.__logger.error( f"更新控件配置失败: {str( e )}" )
			if self.__log_output:
				self.__log_output.append( f"更新控件配置失败: {str( e )}", color="red" )

	def get_supported_operators( self ) -> List[ str ]:
		"""
		获取支持的运算符列表

		Returns:
			List[str]: 支持的运算符列表

		使用示例：
			operators = deleter.get_supported_operators()
			print(f"支持的运算符: {', '.join(operators)}")
		"""
		return list( self.__OPERATORS.keys() )

	def clear_logs( self ) -> None:
		"""
		清空日志输出

		使用示例：
			deleter.clear_logs()
		"""
		if self.__log_output:
			self.__log_output.clear()
		if self.__logger:
			self.__logger.info( "日志已清空" )
		if self.__log_output:
			self.__log_output.append( "日志已清空" )
